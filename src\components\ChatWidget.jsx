
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>utt<PERSON> } from "rsuite";
import { FiMessageSquare, FiX } from "react-icons/fi";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../assets/Style/ChatWidget.css";
import { callOrchestrator } from "../Services/api";
import { marked } from "marked";
import { Resizable } from 'react-resizable';
import 'react-resizable/css/styles.css';
import WechatIcon from "@rsuite/icons/Wechat";

const ChatWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messageInput, setMessageInput] = useState("");
  const [chatHistory, setChatHistory] = useState([]);
  const [isTyping, setIsTyping] = useState(false);
  const [showUpload, setShowUpload] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [flightSearchContext, setFlightSearchContext] = useState(null); // Track current flight search
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isProcessingRefund, setIsProcessingRefund] = useState(false);
  const [uploadStatus, setUploadStatus] = useState('idle'); // 'idle' | 'reading' | 'processing'
  const [pnrProcessingStep, setPnrProcessingStep] = useState(0);
  const [showPnrProcessing, setShowPnrProcessing] = useState(false);
  const chatBodyRef = useRef(null);

  // Add state for scroll-to-bottom functionality
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  // Add state for dimensions
  const [dimensions, setDimensions] = useState({ width: 380, height: 600 });
  const [isDragging, setIsDragging] = useState(false);

  // Add a state to track if we're in fullscreen mode
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Add these preset sizes
  const sizePresets = {
    small: { width: 320, height: 450 },
    medium: { width: 380, height: 600 },
    large: { width: 480, height: 700 },
    fullscreen: { width: window.innerWidth * 0.8, height: window.innerHeight * 0.8 }
  };

  // Add a function to change size
  const changeSize = (preset) => {
    setDimensions(sizePresets[preset]);
  };

  // Handle resize
  const onResize = (event, { size }) => {
    setDimensions({
      width: Math.max(300, size.width), // Minimum width
      height: Math.max(400, size.height) // Minimum height
    });
  };

  // Toggle fullscreen function
  const toggleFullscreen = () => {
    if (isFullscreen) {
      setDimensions(sizePresets.medium);
      setIsFullscreen(false);
    } else {
      setDimensions(sizePresets.fullscreen);
      setIsFullscreen(true);
    }
  };

  // Move normalizeOrchestratorResponse outside of any other function
  const normalizeOrchestratorResponse = (result) => {
    let botMessage;

    if (!result) {
      return { sender: "bot", type: "text", text: "No response received from the server." };

    }

    // Handle refund response
    if (result?.type === "refund_request" && result?.data) {
      const { bookingReference, passengerName, refundAmount, taxAmount, serviceFee, refundReason, refundedTo, refundReceipt } = result.data;
      
      botMessage = {
        sender: "bot",
        type: "refundSummary",
        bookingReference,
        passengerName,
        refundAmount: refundAmount || "Processing",
        taxAmount: taxAmount || "N/A",
        serviceFee: serviceFee || "N/A",
        refundedTo: refundedTo || "Processing",
        refundReason: refundReason || "Ticket Upload",
        receiptLink: refundReceipt || "#"
      };
    } else if (result?.type === "itinerary_details" && result?.data) {
      const { bookingReference, passengerName, segments, requireConfirmation, message } = result.data;
      
      botMessage = {
        sender: "bot",
        type: "itinerary",
        bookingReference,
        passengerName,
        segments: segments || [],
        requireConfirmation: requireConfirmation || false,
        message: message || "Here is your itinerary details",
        suggestUpload: result.suggestUpload || false,
        responseType: result.type
      };
    }
    else if (result?.type === "refund_details" && result?.data) {
      const { bookingReference, passengerName, segments, requireConfirmation, message } = result.data;
      
      botMessage = {
        sender: "bot",
        type: "itinerary",
        bookingReference,
        passengerName,
        segments: segments || [],
        requireConfirmation: true, // Force show confirmation buttons
        message: message || "Here is your itinerary details",
        suggestUpload: result.suggestUpload || false,
        responseType: result.type
      };
    }
    else if (result?.type === "refund_status" && result?.data) {
      const { bookingReference, passengerName, segments, message } = result.data;
      
      botMessage = {
        sender: "bot",
        type: "itinerary",
        bookingReference,
        passengerName,
        segments: segments || [],
        message: message || "Here is your booking status",
        suggestUpload: result.suggestUpload || false,
        responseType: result.type
      };
    } else if (result?.type === "refund_summary" && result?.data) {
      const { bookingReference, passengerName, refundAmount, taxAmount, serviceFee, refundReason, refundedTo, refundReceipt } = result.data;
      
      botMessage = {
        sender: "bot",
        type: "refundSummary",
        bookingReference,
        passengerName,
        refundAmount: refundAmount || "Processing",
        taxAmount: taxAmount || "N/A",
        serviceFee: serviceFee || "N/A",
        refundedTo: refundedTo || "Processing",
        refundReason: refundReason || "Ticket Upload",
        receiptLink: refundReceipt || "#",
        suggestUpload: result.suggestUpload || false,
        responseType: result.type
      };
    } else if (result?.type === "pnr_details" && result?.data) {
      const { 
        bookingReference, 
        customerDetails, 
        segmentDetails, 
        orderNumber, 
        orderStatus,
        totalPrice,
        originDestinationAssociations
      } = result.data;
      
      // Extract passenger names from customerDetails
      const passengers = customerDetails?.map(customer => 
        `${customer.firstName || ''} ${customer.lastName || ''}`
      ).join(', ') || 'Not available';
      
      // Format segments for display
      const segments = segmentDetails?.map(segment => ({
        from: segment.departureAirport?.locationCode || segment.origin || 'N/A',
        to: segment.arrivalAirport?.locationCode || segment.destination || 'N/A',
        airline: segment.marketingAirline?.name || segment.airline || 'N/A',
        flight: segment.marketingAirline?.flightNumber || segment.flightNumber || 'N/A',
        departure: segment.departureDateTime || 'N/A',
        arrival: segment.arrivalDateTime || 'N/A',
        status: segment.status || orderStatus || 'PENDING'
      })) || [];
      
      // Format price
      const price = totalPrice?.monetaryAmount || 'N/A';
      
      botMessage = {
        sender: "bot",
        type: "itinerary",
        bookingReference: bookingReference || orderNumber,
        passengerName: passengers,
        segments: segments,
        price: price,
        orderStatus: orderStatus || 'PENDING',
        message: `Here are the details for booking reference ${bookingReference || orderNumber}`,
        requireConfirmation: false,
        responseType: "pnr_details",
        showRefundEligibilityCheck: true
      };
    } else if (result?.type === "ticket_display" && result?.data) {
      const { pnr, passengerName, segments, ticketAmount, refundEligibility } = result.data;
      
      botMessage = {
        sender: "bot",
        type: "ticketDisplay",
        pnr,
        passengerName,
        segments: segments || [],
        ticketAmount: ticketAmount || "N/A",
        refundEligibility: refundEligibility || { isEligible: false, reason: "Information not available" },
        message: "Here are your ticket details",
        responseType: result.type
      };
    } else {
      botMessage = { 
        sender: "bot", 
        type: "text", 
        text: result.message || "Request processed successfully.",
        suggestUpload: result.suggestUpload || false,
        responseType: result.type || "text"
      };
    }

    return botMessage;
  };

  const toggleChat = () => {
    const wasOpen = isOpen;
    setIsOpen(!isOpen);

    // Add welcome message when opening chat for the first time
    if (!wasOpen && chatHistory.length === 0) {
      setChatHistory([
        { sender: "bot", text: "Hi I am virgino bot, how could I help you today?" }
      ]);
    }
  };

  const scrollToBottom = (smooth = false) => {
    if (chatBodyRef.current) {
      chatBodyRef.current.scrollTo({
        top: chatBodyRef.current.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto'
      });
    }
  };

  // Simplified scroll detection - remove useCallback dependency issues
  const handleScroll = () => {
    if (!chatBodyRef.current) return;

    const element = chatBodyRef.current;
    const { scrollTop, scrollHeight, clientHeight } = element;

    // Check if content is scrollable
    const isScrollable = scrollHeight > clientHeight;

    // Calculate distance from bottom
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

    // Show button if scrollable and user is more than 20px from bottom
    const shouldShowButton = isScrollable && distanceFromBottom > 20;

    console.log('🔄 SCROLL EVENT:', {
      scrollTop: Math.round(scrollTop),
      scrollHeight: Math.round(scrollHeight),
      clientHeight: Math.round(clientHeight),
      distanceFromBottom: Math.round(distanceFromBottom),
      isScrollable,
      shouldShowButton,
      timestamp: new Date().toLocaleTimeString()
    });

    // Direct state update
    setShowScrollToBottom(shouldShowButton);

    // Track user scrolling behavior
    if (shouldShowButton) {
      setIsUserScrolling(true);
    } else if (distanceFromBottom <= 10) {
      setIsUserScrolling(false);
    }
  };

  // Auto-scroll to bottom when new messages arrive (only if user hasn't scrolled up)
  useEffect(() => {
    if (!isUserScrolling) {
      scrollToBottom();
    }
    // Also check if we need to update button visibility when messages change
    setTimeout(() => {
      handleScroll();
    }, 100); // Small delay to ensure DOM is updated
  }, [chatHistory, isTyping]);

  // Initial check for button visibility when component mounts
  useEffect(() => {
    setTimeout(() => {
      handleScroll();
    }, 200); // Delay to ensure DOM is fully rendered
  }, []);

  // Set up scroll event listener
  useEffect(() => {
    const chatBody = chatBodyRef.current;
    if (chatBody) {
      console.log('🔧 Setting up scroll listener');
      chatBody.addEventListener('scroll', handleScroll, { passive: true });
      // Initial check
      setTimeout(handleScroll, 100);
      return () => {
        console.log('🔧 Removing scroll listener');
        chatBody.removeEventListener('scroll', handleScroll);
      };
    }
  }, []); // Remove dependency to avoid re-creating listener

  // Handle scroll to bottom button click
  const handleScrollToBottomClick = () => {
    scrollToBottom(true); // smooth scroll
    setIsUserScrolling(false);
    setShowScrollToBottom(false);
  };

  // Handle date selection for flight search
  const handleDateSelection = (date) => {
    const formattedDate = date.toISOString().split('T')[0]; // YYYY-MM-DD format
    const userFriendlyDate = date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Update flight search context with selected date
    if (flightSearchContext) {
      const updatedContext = {
        ...flightSearchContext,
        date: formattedDate
      };
      setFlightSearchContext(updatedContext);

      // Add user message and search for flights
      setChatHistory((prev) => [
        ...prev,
        { sender: "user", text: `I'd like to travel on ${userFriendlyDate}` },
        {
          sender: "bot",
          text: `Perfect! 🔍 Searching for flights from ${updatedContext.source} to ${updatedContext.destination} on ${userFriendlyDate}...`
        }
      ]);

      // Perform the actual flight search
      setTimeout(async () => {
        try {
          const requestData = {
            type: "flight_search",
            data: {
              source: updatedContext.source,
              destination: updatedContext.destination,
              date: formattedDate
            }
          };

          const result = await callOrchestrator(JSON.stringify(requestData));
          const botMessage = normalizeOrchestratorResponse(result);
          setChatHistory((prev) => [...prev, botMessage]);
        } catch (error) {
          console.error("Failed to search for flights:", error);
          setChatHistory((prev) => [
            ...prev,
            {
              sender: "bot",
              text: `❌ Sorry, there was an error searching for flights. Please try again or contact support.`,
            },
          ]);
        }
      }, 1000);
    }

    setShowDatePicker(false);
    setSelectedDate(null);
  };

  const handleBookFlight = (flight) => {
    // Create a comprehensive booking interface in chat
    setChatHistory((prev) => [
      ...prev,
      {
        sender: "user",
        text: `I want to book ${flight.airline} flight ${flight.id}`
      },
      {
        sender: "bot",
        type: "flightBooking",
        flight: flight,
        text: "Great choice! Let me help you book this flight."
      }
    ]);
  };

  const handleConfirmBooking = (flight, passengerDetails) => {
    setChatHistory((prev) => [
      ...prev,
      {
        sender: "user",
        text: `Confirm booking for ${passengerDetails.name}`
      },
      {
        sender: "bot",
        type: "bookingConfirmation",
        flight: flight,
        passenger: passengerDetails,
        bookingReference: `VB${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
        text: "🎉 Booking confirmed! Here are your details:"
      }
    ]);
  };

  const handleCancelBooking = () => {
    setChatHistory((prev) => [
      ...prev,
      {
        sender: "bot",
        text: "No problem! Feel free to browse other flights or ask me anything else. 😊"
      }
    ]);
  };

  const sendMessage = async (customMessage = null) => {
    const input = customMessage || messageInput.trim();
    if (!input) return;

    const userText = input;

    // Check for flight search keywords
    const flightSearchKeywords = ['flight', 'book flight', 'search flight', 'find flight', 'flights from', 'flights to'];
    const isFlightSearch = flightSearchKeywords.some(keyword => 
      userText.toLowerCase().includes(keyword.toLowerCase())
    );

    if (isFlightSearch) {
      // Extract source, destination, and date from user input
      const fromMatch = userText.match(/from\s+([a-zA-Z\s]+?)(?:\s+to|\s+on|\s*$)/i);
      const toMatch = userText.match(/to\s+([a-zA-Z\s]+?)(?:\s+on|\s+from|\s*$)/i);
      const dateMatch = userText.match(/on\s+(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}|\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2}|today|tomorrow|next week)/i);

      const source = fromMatch?.[1]?.trim();
      const destination = toMatch?.[1]?.trim();
      const dateStr = dateMatch?.[1]?.trim();

      // Update flight search context
      const currentContext = {
        source: source || flightSearchContext?.source,
        destination: destination || flightSearchContext?.destination,
        date: dateStr || flightSearchContext?.date
      };
      setFlightSearchContext(currentContext);

      setChatHistory((prev) => [
        ...prev,
        { sender: "user", text: userText }
      ]);

      // Check what information is missing
      const missingInfo = [];
      if (!currentContext.source) missingInfo.push("departure city");
      if (!currentContext.destination) missingInfo.push("destination city");
      if (!currentContext.date) missingInfo.push("travel date");

      if (missingInfo.length > 0) {
        setChatHistory((prev) => [
          ...prev,
          { 
            sender: "bot", 
            text: `✈️ I'd be happy to help you search for flights! I need some additional information:\n\n${missingInfo.map(info => `• ${info}`).join('\n')}\n\nPlease provide the missing details, for example: "Flights from London to New York on 2024-01-15"` 
          }
        ]);
        
        // Show date picker if only date is missing
        if (missingInfo.length === 1 && missingInfo[0] === "travel date") {
          setShowDatePicker(true);
        }
      }

      // If all information is provided, proceed with flight search
      if (currentContext.source && currentContext.destination && currentContext.date) {
        setChatHistory((prev) => [
          ...prev,
          {
            sender: "bot",
            text: `🔍 Searching for flights from ${currentContext.source} to ${currentContext.destination} on ${currentContext.date}...`
          }
        ]);

        // Perform flight search
        try {
          const requestData = {
            type: "flight_search",
            data: {
              source: currentContext.source,
              destination: currentContext.destination,
              date: currentContext.date
            }
          };

          const result = await callOrchestrator(JSON.stringify(requestData));
          const botMessage = normalizeOrchestratorResponse(result);
          setChatHistory((prev) => [...prev, botMessage]);
        } catch (error) {
          console.error("Failed to search for flights:", error);
          setChatHistory((prev) => [
            ...prev,
            {
              sender: "bot",
              text: `❌ Sorry, there was an error searching for flights: ${error.message}. Please try again or contact support.`,
            },
          ]);
        }
      }

      setMessageInput("");
      return;
    }

    // Check if user is updating flight search parameters (changing source/destination)
    const isUpdatingFlightSearch = flightSearchContext && (
      userText.toLowerCase().includes('change') ||
      userText.toLowerCase().includes('different') ||
      userText.toLowerCase().includes('instead') ||
      (userText.match(/from\s+([a-zA-Z\s]+)/i) || userText.match(/to\s+([a-zA-Z\s]+)/i))
    );

    if (isUpdatingFlightSearch && !isFlightSearch) {
      const updatedContext = {
        ...flightSearchContext,
        source: newSource || flightSearchContext.source,
        destination: newDestination || flightSearchContext.destination,
        date: null // Reset date when changing route
      };
      setFlightSearchContext(updatedContext);

      setChatHistory((prev) => [
        ...prev,
        { sender: "user", text: userText },
        { 
          sender: "bot", 
          text: `✈️ Updated your search to: ${updatedContext.source} to ${updatedContext.destination}\n\nPlease provide your travel date to search for flights.` 
        }
      ]);
      
      setShowDatePicker(true); // Automatically show date picker
    }

    // Add condition to check for refund-related keywords
    if (userText.toLowerCase().includes("refund") ||
        userText.toLowerCase().includes("money back") ||
        userText.toLowerCase().includes("cancel")) {
      setChatHistory((prev) => [
        ...prev,
        { sender: "user", text: userText },
        {
          sender: "bot",
          text: "📎 Got it. Please upload your ticket or provide your booking reference and passenger name"
        },
      ]);
      setShowUpload(true);
      setMessageInput("");
      return;
    }

    // Add this to your existing message handling logic
    if (userText.toLowerCase().includes("show ticket") || 
        userText.toLowerCase().includes("view ticket") || 
        userText.toLowerCase().includes("my ticket")) {
      setChatHistory((prev) => [
        ...prev,
        { sender: "user", text: userText },
        { 
          sender: "bot", 
          text: "Please provide your booking reference (PNR) and passenger name to view your ticket details." 
        },
      ]);
      setMessageInput("");
      return;
    }

    // Add this to your pattern matching logic for PNR extraction
    const pnrPattern = /\b([A-Z0-9]{5,6})\b/i;
    const namePattern = /name\s*(?:is|:)?\s*([A-Za-z\s]+)(?:\.|$)/i;

    const pnrMatch = userText.match(pnrPattern);
    const nameMatch = userText.match(namePattern);

    if (pnrMatch && nameMatch) {
      const pnr = pnrMatch[1];
      const passengerName = nameMatch[1].trim();

      setChatHistory((prev) => [
        ...prev,
        { sender: "user", text: userText }
      ]);

      // Show PNR processing animation
      setShowPnrProcessing(true);

      try {
        // Simulate processing time to show the animation (12 seconds for all 4 steps)
        await new Promise(resolve => setTimeout(resolve, 12000));

        const requestData = {
          type: "ticket_display",
          data: {
            bookingReference: pnr,
            passengerName
          }
        };

        const result = await callOrchestrator(JSON.stringify(requestData));

        // Hide processing animation
        setShowPnrProcessing(false);

        const botMessage = normalizeOrchestratorResponse(result);
        setChatHistory((prev) => [...prev, botMessage]);
      } catch (error) {
        console.error("Failed to retrieve ticket:", error);

        // Hide processing animation on error
        setShowPnrProcessing(false);

        setChatHistory((prev) => [
          ...prev,
          {
            sender: "bot",
            text: `❌ Sorry, there was an error retrieving your ticket: ${error.message}. Please try again or contact support.`,
          },
        ]);
      }
      
      setMessageInput("");
      return;
    }

    // Existing upload check
    if (userText.toLowerCase().includes("upload")) {
      setChatHistory((prev) => [
        ...prev,
        { sender: "user", text: userText },
        { sender: "bot", text: "📤 Please upload your ticket file below." },
      ]);
      setShowUpload(true);
      setMessageInput("");
      return;
    }

    const userMessage = { sender: "user", text: userText };
    setChatHistory((prev) => [...prev, userMessage]);
    setMessageInput("");
    setIsTyping(true);
    setShowUpload(false);

    try {
      // Centralized response normalization
// Centralized response normalization
const normalizeOrchestratorResponse = (result, setShowDatePicker, setShowUpload) => {
  let botMessage;

  if (!result) {
    return { sender: "bot", type: "text", text: "No response received from the server." };
  }

  // Handle refund response
  if (result?.type === "refund_request" && result?.data) {
    const { bookingReference, passengerName, refundAmount, taxAmount, serviceFee, refundReason, refundedTo, refundReceipt } = result.data;
    
    botMessage = {
      sender: "bot",
      type: "refundSummary",
      bookingReference,
      passengerName,
      refundAmount: refundAmount || "Processing",
      taxAmount: taxAmount || "N/A",
      serviceFee: serviceFee || "N/A",
      refundedTo: refundedTo || "Processing",
      refundReason: refundReason || "Ticket Upload",
      receiptLink: refundReceipt || "#"
    };
  } else if (result?.type === "itinerary_details" && result?.data) {
    const { bookingReference, passengerName, segments, requireConfirmation, message } = result.data;
    
    botMessage = {
      sender: "bot",
      type: "itinerary",
      bookingReference,
      passengerName,
      segments: segments || [],
      requireConfirmation: requireConfirmation || false,
      message: message || "Here is your itinerary details",
      suggestUpload: result.suggestUpload || false,
      responseType: result.type
    };
  } 

  else if ((result?.type === "refundItinerary" && result?.data) || (result?.content?.type === "refundItinerary" && result?.content?.data)) {
    const { bookingReference, passengerName, segments, requireConfirmation, message } = result.data !== undefined ? result.data : result.content.data;
    
    botMessage = {
      sender: "bot",
      type: "itinerary",
      bookingReference,
      passengerName,
      segments: segments || [],
      requireConfirmation: requireConfirmation || false,
      message: message || "Here is your itinerary details",
      suggestUpload: result.suggestUpload || false,
      responseType: result.type
    };
  }
  
  else if (result?.type === "refund_status" && result?.data) {
    const { bookingReference, passengerName, segments, message } = result.data;
    
    botMessage = {
      sender: "bot",
      type: "itinerary",
      bookingReference,
      passengerName,
      segments: segments || [],
      message: message || "Here is your booking status",
      suggestUpload: result.suggestUpload || false,
      responseType: result.type
    };
  } else if (result?.type === "refund_summary" && result?.data) {
    const { bookingReference, passengerName, refundAmount, taxAmount, serviceFee, refundReason, refundedTo, refundReceipt } = result.data;
    
    botMessage = {
      sender: "bot",
      type: "refundSummary",
      bookingReference,
      passengerName,
      refundAmount: refundAmount || "Processing",
      taxAmount: taxAmount || "N/A",
      serviceFee: serviceFee || "N/A",
      refundedTo: refundedTo || "Processing",
      refundReason: refundReason || "Ticket Upload",
      receiptLink: refundReceipt || "#",
      suggestUpload: result.suggestUpload || false,
      responseType: result.type
    };
  } else {
    botMessage = { 
      sender: "bot", 
      type: "text", 
      text: result.message || "Request processed successfully.",
      suggestUpload: result.suggestUpload || false,
      responseType: result.type || "text"
    };
  }

  return botMessage;
};


// Usage in your main handler
const result = await callOrchestrator(userText);
console.log("Raw Orchestrator Response:", result);
const botMessage = normalizeOrchestratorResponse(result, setShowDatePicker, setShowUpload);
setChatHistory((prev) => [...prev, botMessage]);

    } catch (error) {
      console.error("Orchestrator call error:", error);
      setChatHistory((prev) => [
        ...prev,
        { sender: "bot", text: "⚠️ Failed to reach Orchestrator." },
      ]);
    } finally {
      setIsTyping(false);
    }
  };
  const submitUpload = () => {
    if (!uploadedFile) return;
    
    // Show "Reading ticket..." status
    setUploadStatus('reading');
    
    const reader = new FileReader();
    reader.onload = async (e) => {
      const content = e.target.result;
      
      // Hide upload section immediately after reading starts
      setShowUpload(false);
      
      const nameMatch = content.match(/Name:\s*([A-Za-z\s]+)(?:\r?\n|$)/i);
      const pnrMatch = content.match(/PNR:\s*([A-Za-z0-9]+)/i);
      const flightMatch = content.match(/Flight:\s*([A-Z]{3}\s*to\s*[A-Z]{3})/i);
      const dateMatch = content.match(/Date:\s*(\d{4}-\d{2}-\d{2})/i);

      const passengerName = nameMatch?.[1]?.trim();
      const bookingReference = pnrMatch?.[1]?.trim();
      const flightRoute = flightMatch?.[1]?.trim();
      const travelDate = dateMatch?.[1]?.trim();

      const chatUpdates = [
        { 
          sender: "user", 
          text: `📎 Uploaded: ${uploadedFile.name}` 
        }
      ];

      if (bookingReference && passengerName) {
        chatUpdates.push({
          sender: "bot",
          text: `✅ Extracted from uploaded ticket:
📄 Booking Ref (PNR): **${bookingReference}**
👤 Passenger: **${passengerName}**
✈️ Flight Route: **${flightRoute || 'Not specified'}**
📅 Travel Date: **${travelDate || 'Not specified'}**`,
        });
        
        // Update status to processing
        setUploadStatus('processing');
        
        chatUpdates.push({ 
          sender: "bot", 
          type: "processing", 
          text: "🔄 Processing your refund request..." 
        });
        
        setIsProcessingRefund(true);
        setChatHistory((prev) => [...prev, ...chatUpdates]);

        try {
          const requestData = {
            type: "refund_request",
            data: {
              bookingReference,
              passengerName,
              flightRoute,
              travelDate,
              source: "ticket_upload"
            }
          };

          const result = await callOrchestrator(JSON.stringify(requestData));
          
          if (!result) {
            throw new Error("Empty response from orchestrator");
          }

          const botMessage = normalizeOrchestratorResponse(result);
          setChatHistory((prev) => [...prev, botMessage]);

        } catch (error) {
          console.error("Failed to process refund:", error);
          setChatHistory((prev) => [
            ...prev,
            {
              sender: "bot",
              text: `❌ Sorry, there was an error processing your refund request: ${error.message}. Please try again or contact support.`,
            },
          ]);
        } finally {
          setIsProcessingRefund(false);
          setUploadStatus('idle');
        }
      } else {
        chatUpdates.push({
          sender: "bot",
          text: "❌ Could not extract required booking information from the ticket. Please ensure the ticket contains valid PNR and passenger name.",
        });
        setChatHistory((prev) => [...prev, ...chatUpdates]);
        setUploadStatus('idle');
      }

      setUploadedFile(null);
    };
    reader.readAsText(uploadedFile);
  };
  
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      setUploadedFile(file);
      setUploadStatus('reading');
      
      const reader = new FileReader();
      reader.onload = async (e) => {
        const content = e.target.result;
        
        setShowUpload(false);
        
        const nameMatch = content.match(/Name:\s*([A-Za-z\s]+)(?:\r?\n|$)/i);
        const pnrMatch = content.match(/PNR:\s*([A-Za-z0-9]+)/i);
        const flightMatch = content.match(/Flight:\s*([A-Z]{3}\s*to\s*[A-Z]{3})/i);
        const dateMatch = content.match(/Date:\s*(\d{4}-\d{2}-\d{2})/i);
        const amountMatch = content.match(/Amount:\s*\$(\d+(\.\d{2})?)/i);

        const passengerName = nameMatch?.[1]?.trim();
        const bookingReference = pnrMatch?.[1]?.trim();
        const flightRoute = flightMatch?.[1]?.trim();
        const travelDate = dateMatch?.[1]?.trim();
        const ticketAmount = amountMatch?.[1] ? `$${amountMatch[1]}` : "N/A";

        if (bookingReference && passengerName) {
          // First show the upload confirmation
          setChatHistory((prev) => [
            ...prev,
            {
              sender: "user",
              text: `📎 Uploaded: ${file.name}`
            }
          ]);

          // Show PNR processing animation for file upload
          setShowPnrProcessing(true);

          // Simulate processing time to show the animation
          await new Promise(resolve => setTimeout(resolve, 12000));

          // Hide processing animation and show results
          setShowPnrProcessing(false);

          setChatHistory((prev) => [
            ...prev,
            {
              sender: "bot",
              type: "Ticketdetail",
              bookingReference,
              passengerName,
              flightNumber: "VA123",
              departure: "London",
              destination: "New York",
              travelDate,
              ticketAmount,
            },
          ]);

          setUploadStatus('idle');
        } else {
          setChatHistory((prev) => [
            ...prev,
            {
              sender: "bot",
              text: "❌ Could not extract required booking information from the ticket. Please ensure the ticket contains valid PNR and passenger name.",
            },
          ]);
          setUploadStatus('idle');
        }
      };
      reader.readAsText(file);
    }
  };
  
  const handleConfirmRefund = async (bookingReference, passengerName) => {
    setIsProcessingRefund(true);
    setChatHistory((prev) => [
      ...prev,
      { 
        sender: "bot", 
        type: "processing", 
        text: "🔄 Processing your refund request..." 
      }
    ]);

    try {
      const requestData = {
        type: "refund_request",
        data: {
          bookingReference,
          passengerName,
          source: "ticket_upload"
        }
      };

      const result = await callOrchestrator(JSON.stringify(requestData));
      const botMessage = normalizeOrchestratorResponse(result);
      setChatHistory((prev) => [...prev, botMessage]);
    } catch (error) {
      console.error("Failed to process refund:", error);
      setChatHistory((prev) => [
        ...prev,
        {
          sender: "bot",
          text: `❌ Sorry, there was an error processing your refund request: ${error.message}. Please try again or contact support.`,
        },
      ]);
    } finally {
      setIsProcessingRefund(false);
    }
  };
  
  const ProcessingIndicator = () => (
    <div className="processing-indicator">
      {uploadStatus === 'reading' && (
        <div className="processing-message">
          <span className="spinner">📄</span> Reading ticket...
        </div>
      )}
      {uploadStatus === 'processing' && (
        <div className="processing-message">
          <span className="spinner">🔄</span> Processing refund request...
        </div>
      )}
    </div>
  );

  // PNR Processing Animation Component
  const PnrProcessingAnimation = () => {
    const [currentStep, setCurrentStep] = useState(0);
    const [streamingMessages, setStreamingMessages] = useState([]);
    const [currentStreamIndex, setCurrentStreamIndex] = useState(0);

    const processingSteps = [
      {
        id: 1,
        agent: "PNR Validator",
        avatar: "🔍",
        message: "Verifying PNR format and structure...",
        streamMessages: [
          "Checking PNR format...",
          "Validating booking reference...",
          "PNR format verified ✓"
        ],
        complete: false
      },
      {
        id: 2,
        agent: "Airline System",
        avatar: "✈️",
        message: "Connecting to airline database...",
        streamMessages: [
          "Establishing secure connection...",
          "Authenticating with airline systems...",
          "Connected to Virgin Atlantic database ✓"
        ],
        complete: false
      },
      {
        id: 3,
        agent: "PNR Detail Agent",
        avatar: "📋",
        message: "Calling PNR detail agent...",
        streamMessages: [
          "Initiating PNR lookup...",
          "Retrieving passenger information...",
          "Fetching flight segments...",
          "Booking details retrieved ✓"
        ],
        complete: false
      },
      {
        id: 4,
        agent: "Data Processor",
        avatar: "⚡",
        message: "Processing itinerary information...",
        streamMessages: [
          "Parsing flight data...",
          "Calculating fare details...",
          "Finalizing ticket information...",
          "Processing complete ✓"
        ],
        complete: false
      }
    ];

    useEffect(() => {
      if (!showPnrProcessing) return;

      const stepInterval = setInterval(() => {
        setCurrentStep((prev) => {
          if (prev < processingSteps.length - 1) {
            return prev + 1;
          }
          return prev;
        });
      }, 3000); // Change step every 3 seconds

      return () => clearInterval(stepInterval);
    }, [showPnrProcessing, processingSteps.length]);

    // Handle streaming messages for current step
    useEffect(() => {
      if (!showPnrProcessing || currentStep >= processingSteps.length) return;

      const currentStepData = processingSteps[currentStep];
      let messageIndex = 0;
      setStreamingMessages([]);
      setCurrentStreamIndex(0);

      const messageInterval = setInterval(() => {
        if (messageIndex < currentStepData.streamMessages.length) {
          const newMessage = {
            id: Date.now() + messageIndex,
            text: currentStepData.streamMessages[messageIndex],
            isDisappearing: false
          };

          setStreamingMessages(prev => [...prev, newMessage]);
          setCurrentStreamIndex(messageIndex);
          messageIndex++;
        } else {
          clearInterval(messageInterval);
        }
      }, 800); // New message every 800ms

      // Clear messages when step changes
      const clearTimeout = setTimeout(() => {
        setStreamingMessages(prev =>
          prev.map(msg => ({ ...msg, isDisappearing: true }))
        );
      }, 2500);

      return () => {
        clearInterval(messageInterval);
        clearTimeout(clearTimeout);
      };
    }, [currentStep, showPnrProcessing]);

    if (!showPnrProcessing) return null;

    return (
      <div className="chat-message bot pnr-processing-container">
        <div className="pnr-processing-header">
          <div className="processing-title">🔍 Processing PNR Details</div>
          <div className="processing-subtitle">Connecting to multiple systems...</div>
        </div>

        <div className="pnr-processing-steps">
          {processingSteps.map((step, index) => (
            <div
              key={step.id}
              className={`pnr-processing-step ${index === currentStep ? 'current' : ''} ${index < currentStep ? 'complete' : ''}`}
            >
              <div className="step-agent-info">
                <div className="agent-avatar">
                  <span>{step.avatar}</span>
                </div>
                <div className="agent-details">
                  <div className="agent-name">{step.agent}</div>
                  <div className="agent-message">
                    {step.message}
                    {index === currentStep && (
                      <span className="typing-dots">
                        <span className="dot"></span>
                        <span className="dot"></span>
                        <span className="dot"></span>
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className="step-status">
                {index < currentStep ? (
                  <span className="step-complete">✓</span>
                ) : index === currentStep ? (
                  <div className="step-spinner"></div>
                ) : (
                  <span className="step-pending">⏳</span>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Streaming Messages Section */}
        <div className="streaming-messages-container">
          {streamingMessages.map((message) => (
            <div
              key={message.id}
              className={`streaming-message ${message.isDisappearing ? 'disappearing' : 'appearing'}`}
            >
              <span className="stream-indicator">▶</span>
              <span className="stream-text">{message.text}</span>
            </div>
          ))}
        </div>

        <div className="data-stream-animation">
          <div className="data-stream">
            <div className="data-line"></div>
            <div className="data-line"></div>
            <div className="data-line"></div>
            <div className="data-line"></div>
          </div>
        </div>
      </div>
    );
  };
  
  const handleCheckRefundEligibility = async (pnr, passengerName) => {
    // Show PNR processing animation
    setShowPnrProcessing(true);
    setPnrProcessingStep(0);

    try {
      // Simulate processing time to show the animation (8 seconds for all 4 steps)
      await new Promise(resolve => setTimeout(resolve, 8000));

      const requestData = {
        type: "refund_eligibility_check",
        data: {
          bookingReference: pnr,
          passengerName,
          source: "eligibility_check"
        }
      };

      const result = await callOrchestrator(JSON.stringify(requestData));

      if (!result) {
        throw new Error("No response received from orchestrator");
      }

      // Hide processing animation
      setShowPnrProcessing(false);
      setPnrProcessingStep(0);

      // Mock eligibility response for demonstration
      const eligibilityResult = {
        sender: "bot",
        type: "refund_eligibility",
        bookingReference: pnr,
        passengerName: passengerName,
        isEligible: true,
        eligibilityReason: "Ticket is eligible for full refund as per fare rules",
        refundAmount: "£450.00",
        processingFee: "£25.00",
        netRefund: "£425.00",
        processingTime: "5-7 business days",
        message: "✅ Good news! Your booking is eligible for refund."
      };

      setChatHistory((prev) => [...prev, eligibilityResult]);

    } catch (error) {
      console.error("Failed to check refund eligibility:", error);

      // Hide processing animation on error
      setShowPnrProcessing(false);
      setPnrProcessingStep(0);

      setChatHistory((prev) => [
        ...prev,
        {
          sender: "bot",
          text: `❌ Sorry, there was an error checking refund eligibility: ${error.message}. Please try again or contact support.`,
        },
      ]);
    }
  };

  const handleSkipRefundCheck = (messageIndex) => {
    // Update the specific message to hide the refund eligibility check
    setChatHistory((prev) =>
      prev.map((msg, idx) =>
        idx === messageIndex
          ? { ...msg, showRefundEligibilityCheck: false }
          : msg
      )
    );
  };

  const handleInitiateRefund = async (pnr, passengerName) => {
    if (!pnr) {
      setChatHistory((prev) => [
        ...prev,
        { sender: "bot", text: "❌ PNR is required to initiate refund" }
      ]);
      return;
    }

    setChatHistory((prev) => [
      ...prev,
      { sender: "bot", type: "processing", text: "🔄 Processing your refund request..." }
    ]);

    setIsProcessingRefund(true);

    try {
      const requestData = {
        type: "refund_request",
        data: {
          bookingReference: pnr,
          passengerName,
          source: "chat_refund"
        }
      };

      const result = await callOrchestrator(JSON.stringify(requestData));

      if (!result) {
        throw new Error("No response received from orchestrator");
      }

      const botMessage = normalizeOrchestratorResponse(result);
      setChatHistory((prev) => [...prev, botMessage]);

    } catch (error) {
      console.error("Failed to process refund:", error);
      setChatHistory((prev) => [
        ...prev,
        {
          sender: "bot",
          text: `❌ Sorry, there was an error processing your refund request: ${error.message}. Please try again or contact support.`,
        },
      ]);
    } finally {
      setIsProcessingRefund(false);
    }
  };
  
  return (
    <div className="chat-widget">
      {isOpen ? (
        <div className="resizable-container">
          <Resizable
            width={dimensions.width}
            height={dimensions.height}
            onResize={onResize}
            onResizeStart={() => setIsDragging(true)}
            onResizeStop={() => setIsDragging(false)}
            minConstraints={[300, 400]}
            maxConstraints={[800, 800]}
          >
            <div
              className={`chat-window ${isDragging ? 'resizing' : ''}`}
              style={{
                width: dimensions.width,
                height: dimensions.height,
                position: 'relative' // For absolute positioning of scroll button
              }}
            >
              <div className="chat-header" onDoubleClick={toggleFullscreen}>
                <div className="chat-title">
                  <WechatIcon style={{ fontSize: '20px' }} />
                  <span>Virgin Atlantic Assistant</span>
                </div>
                <div className="chat-controls">
                  <div className="size-controls">
                    <button 
                      className="size-btn small" 
                      onClick={() => changeSize('small')} 
                      title="Small"
                    >
                      -
                    </button>
                    <button 
                      className="size-btn medium" 
                      onClick={() => changeSize('medium')} 
                      title="Medium"
                    >
                      □
                    </button>
                    <button 
                      className="size-btn large" 
                      onClick={() => changeSize('large')} 
                      title="Large"
                    >
                      +
                    </button>
                    <button 
                      className="size-btn fullscreen" 
                      onClick={() => changeSize('fullscreen')} 
                      title="Fullscreen"
                    >
                      ⤢
                    </button>
                  </div>
                  <button className="close-btn" onClick={toggleChat}>
                    ×
                  </button>
                </div>
              </div>

              <div className="chat-body" ref={chatBodyRef} style={{
                height: dimensions.height - 140, // Increased to account for input section
                maxHeight: dimensions.height - 140,
                overflowY: 'auto',
                position: 'relative' // Add this for absolute positioning of scroll button
              }}>
                {chatHistory.map((msg, idx) => (
                  <div key={idx} className={`chat-message ${msg.sender}`} style={{
                    padding: '8px 16px',
                    display: 'flex',
                    flexDirection: msg.sender === 'user' ? 'row-reverse' : 'row',
                    alignItems: 'flex-start',
                    gap: '8px'
                  }}>
                    {msg.type === "flightList" && Array.isArray(msg.flights) ? (
                      <div className="flight-list">
                        <h4 className="flight-heading">✈️ Available Flights</h4>
                        {msg.flights.map((flight) => (
                          <div className="flight-card" key={flight.id}>
                            <div className="flight-details">
                              <div className="flight-detail-block">
                                🛫 <strong>Airline:</strong>
                                <div>{flight.airline}</div>
                              </div>
                              <div className="flight-detail-block">
                                🆔 <strong>Flight ID:</strong>
                                <div>{flight.id}</div>
                              </div>
                              <div className="flight-detail-block">
                                ⏰ <strong>Departure:</strong>
                                <div>{formatTime(flight.departureTime)}</div>
                              </div>
                              <div className="flight-detail-block">
                                🛬 <strong>Arrival:</strong>
                                <div>{formatTime(flight.arrivalTime)}</div>
                              </div>
                              <div className="flight-detail-block">
                                💰 <strong>Price:</strong>
                                <div>₹{flight.price.toLocaleString()}</div>
                              </div>
                            </div>
                            <button className="book-button" onClick={() => handleBookFlight(flight)}>
                              Book Now
                            </button>
                          </div>
                        ))}
                      </div>
                      ) : msg.type === "multiLegFlightList" && Array.isArray(msg.results) ? (
                        <div className="multi-leg-flights">
                          <h4 className="flight-heading">✈️ Multi-Leg Flight Results</h4>
                          {msg.results.map((leg) => (
                            <div key={leg.legNumber} className="flight-leg">
                              <h5>Leg {leg.legNumber}</h5>
                              {leg.flights.map((flight) => (
                                <div className="flight-card" key={`${leg.legNumber}-${flight.id}`}>
                                  <div className="flight-details">
                                    <div><strong>🛫 Airline:</strong> {flight.airline}</div>
                                    <div><strong>🆔 Flight ID:</strong> {flight.id}</div>
                                    <div><strong>🕒 Departure:</strong> {formatTime(flight.departureTime)}</div>
                                    <div><strong>🛬 Arrival:</strong> {formatTime(flight.arrivalTime)}</div>
                                    <div><strong>💰 Price:</strong> ₹{flight.price?.toLocaleString("en-IN")}</div>
                                  </div>
                                  <button className="book-button" onClick={() => handleBookFlight(flight)}>
                                    Book Now
                                  </button>
                                </div>
                              ))}
                            </div>
                          ))}
                        </div>
                      ) 
                      : msg.type === "Ticketdetail" ? (
                        <div className="ticket-summary">
                          <div className="ticket-header">
                            <h4>🎫 Uploaded Ticket Details</h4>
                          </div>
                          <div className="ticket-details">
                            <div className="ticket-line">📄 Booking Reference: <strong>{msg.bookingReference}</strong></div>
                            <div className="ticket-line">👤 Passenger Name: <strong>{msg.passengerName}</strong></div>
                            <div className="ticket-line">✈️ Flight: <strong>{msg.flightNumber}</strong></div>
                            <div className="ticket-line">🛫 From: <strong>{msg.departure}</strong></div>
                            <div className="ticket-line">🛬 To: <strong>{msg.destination}</strong></div>
                            <div className="ticket-line">📅 Travel Date: <strong>{msg.travelDate}</strong></div>
                            <div className="ticket-line">💰 Ticket Amount: <strong>{msg.ticketAmount}</strong></div>
                          </div>
                          <div className="ticket-actions">
                            <button 
                              className="verify-ticket-btn"
                              onClick={() => handleConfirmRefund(msg.bookingReference, msg.passengerName)}
                            >
                              Verify Ticket
                            </button>
                          </div>
                        </div>
                      ) : msg.type === "itinerary" ? (
                        <div className="itinerary-container">
                          <div className="itinerary-header">
                            <h4>✈️ Flight Itinerary</h4>
                            <div className="booking-info">
                              <span>📄 PNR: <strong>{msg.bookingReference}</strong></span>
                              <span>👤 Passenger: <strong>{msg.passengerName}</strong></span>
                            </div>
                          </div>
                          
                          <div className="segments-container">
                            {msg.segments?.map((segment, index) => (
                              <div key={index} className="segment-card">
                                <div className="segment-header">
                                  <span className="airline-logo">
                                    <img src={`/airlines/${segment.airline.toLowerCase()}.png`} alt={segment.airline} />
                                    {segment.airline}
                                  </span>
                                  <span className="flight-number">{segment.flightNumber}</span>
                                </div>
                                
                                <div className="flight-route">
                                  <div className="origin">
                                    <div className="city-code">{segment.origin}</div>
                                    <div className="datetime">
                                      {new Date(segment.departureDate).toLocaleDateString('en-US', {
                                        day: 'numeric',
                                        month: 'short',
                                        year: 'numeric'
                                      })}
                                      <br />
                                      {new Date(segment.departureDate).toLocaleTimeString('en-US', {
                                        hour: '2-digit',
                                        minute: '2-digit'
                                      })}
                                    </div>
                                  </div>
                                  
                                  <div className="flight-line">
                                    <div className="line"></div>
                                    <div className="airplane-icon">✈️</div>
                                  </div>
                                  
                                  <div className="destination">
                                    <div className="city-code">{segment.destination}</div>
                                    <div className="datetime">
                                      {new Date(segment.arrivalDate).toLocaleDateString('en-US', {
                                        day: 'numeric',
                                        month: 'short',
                                        year: 'numeric'
                                      })}
                                      <br />
                                      {new Date(segment.arrivalDate).toLocaleTimeString('en-US', {
                                        hour: '2-digit',
                                        minute: '2-digit'
                                      })}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>

                          {msg.responseType === "refund_status" && (
                            <div className="refund-confirmation">
                              <div className="refund-warning">
                                ⚠️ Are you sure you want to process a refund for this booking?
                              </div>
                              <div className="refund-details">
                                <p>• Refund will be processed for PNR: <strong>{msg.bookingReference}</strong></p>
                                <p>• Passenger Name: <strong>{msg.passengerName}</strong></p>
                                <p>• Processing time may take 5-7 business days</p>
                              </div>
                              <div className="refund-actions">
                                <button 
                                  className="confirm-refund-btn"
                                  onClick={() => handleConfirmRefund(msg.bookingReference, msg.passengerName)}
                                >
                                  Confirm Refund
                                </button>
                                <button className="cancel-refund-btn">
                                  Cancel
                                </button>
                              </div>
                            </div>
                          )}

                          {msg.showRefundEligibilityCheck && msg.responseType === "pnr_details" && (
                            <div className="refund-eligibility-check">
                              <div className="eligibility-question">
                                <p>💰 Are you interested to check refund eligibility for this booking?</p>
                              </div>
                              <div className="eligibility-actions">
                                <button
                                  className="check-eligibility-btn"
                                  onClick={() => handleCheckRefundEligibility(msg.bookingReference, msg.passengerName)}
                                >
                                  Check Refund Eligibility
                                </button>
                                <button
                                  className="skip-eligibility-btn"
                                  onClick={() => handleSkipRefundCheck(idx)}
                                >
                                  No, Thanks
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : msg.type === "refund_status" ? (
                        <div className="itinerary-summary">
                          <div className="itinerary-header">
                            <h4>{msg.message || "✈️ Refund Status"}</h4>
                          </div>
                          <div className="itinerary-details">
                            <div className="itinerary-line">📄 Booking Reference: <strong>{msg.bookingReference}</strong></div>
                            <div className="itinerary-line">👤 Passenger Name: <strong>{msg.passengerName}</strong></div>
                            {msg.segments && msg.segments.map((segment, index) => (
                              <div key={index} className="segment-details">
                                <div className="itinerary-line">✈️ Flight: <strong>{segment.flightNumber}</strong></div>
                                <div className="itinerary-line">🛫 From: <strong>{segment.from}</strong></div>
                                <div className="itinerary-line">🛬 To: <strong>{segment.to}</strong></div>
                                <div className="itinerary-line">⏰ Departure: <strong>{segment.departureTime}</strong></div>
                                <div className="itinerary-line">⏰ Arrival: <strong>{segment.arrivalTime}</strong></div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : msg.type === "refundSummary" ? (
                    <div className="refund-summary">
                      <div className="refund-line"><strong>✅ Refund successfully initiated for your booking.</strong></div>
                      <div className="refund-line">📄 Booking Ref: <strong>{msg.bookingReference}</strong></div>
                      <div className="refund-line">👤 Passenger: <strong>{msg.passengerName}</strong></div>
                      <div className="refund-line">💰 Refund Amount: <strong>{msg.refundAmount}</strong></div>
                      <div className="refund-line">🧞 Tax Deducted: <strong>{msg.taxAmount}</strong></div>
                      <div className="refund-line">🔧 Service Fee: <strong>{msg.serviceFee}</strong></div>
                      <div className="refund-line">💳 Refunded To: <strong>{msg.refundedTo}</strong></div>
                      <div className="refund-line">📝 Refund Reason: <strong>{msg.refundReason}</strong></div>
                      {msg.receiptLink && (
                        <div className="refund-line">
                          📅 <a href={msg.receiptLink} target="_blank" rel="noopener noreferrer">Download Receipt</a>
                        </div>
                      )}
                    </div>
                  ) : msg.type === "refund_eligibility" ? (
                    <div className="refund-eligibility-result">
                      <div className="eligibility-header">
                        <h4>{msg.message}</h4>
                      </div>
                      <div className="eligibility-details">
                        <div className="eligibility-line">📄 Booking Reference: <strong>{msg.bookingReference}</strong></div>
                        <div className="eligibility-line">👤 Passenger Name: <strong>{msg.passengerName}</strong></div>
                        <div className="eligibility-line">✅ Status: <strong>{msg.isEligible ? 'Eligible for Refund' : 'Not Eligible'}</strong></div>
                        <div className="eligibility-line">📝 Reason: <strong>{msg.eligibilityReason}</strong></div>
                        {msg.isEligible && (
                          <>
                            <div className="eligibility-line">💰 Refund Amount: <strong>{msg.refundAmount}</strong></div>
                            <div className="eligibility-line">🔧 Processing Fee: <strong>{msg.processingFee}</strong></div>
                            <div className="eligibility-line">💵 Net Refund: <strong>{msg.netRefund}</strong></div>
                            <div className="eligibility-line">⏱️ Processing Time: <strong>{msg.processingTime}</strong></div>
                          </>
                        )}
                      </div>
                      {msg.isEligible && (
                        <div className="eligibility-actions">
                          <button
                            className="proceed-refund-btn"
                            onClick={() => handleInitiateRefund(msg.bookingReference, msg.passengerName)}
                          >
                            Proceed with Refund
                          </button>
                          <button className="cancel-refund-btn">
                            Cancel
                          </button>
                        </div>
                      )}
                    </div>
                  ) : msg.type === "flightBooking" ? (
                    <FlightBookingInterface
                      flight={msg.flight}
                      onConfirm={handleConfirmBooking}
                      onCancel={handleCancelBooking}
                    />
                  ) : msg.type === "bookingConfirmation" ? (
                    <BookingConfirmationInterface
                      flight={msg.flight}
                      passenger={msg.passenger}
                      bookingReference={msg.bookingReference}
                    />
                  ) : msg.type === "processing" ? (
                    <div className="spinner-line">
                      <span className="spinner"></span> {msg.text}
                    </div>
                  ) : msg.sender === "bot" ? (
                    <div className="bot-message-text">
                      {typeof msg.text === "string" ? (
                        <span dangerouslySetInnerHTML={{ __html: marked.parse(msg.text) }}></span>
                      ) : (
                        <pre>{JSON.stringify(msg.text, null, 2)}</pre>
                      )}
                    </div>
                  ) : (
                    <div className="user-message-text">{msg.text}</div>
                  )}
                </div>
              ))}
  
            {showDatePicker && (
              <div className="chat-message bot">
                <div className="chatbot-date-picker">
                  <div className="date-picker-message">
                    <span className="date-picker-icon">📅</span>
                    <span>When would you like to travel?</span>
                  </div>

                  {/* Quick Date Chips */}
                  <div className="date-chips-container">
                    <button
                      className="date-chip"
                      onClick={() => {
                        const today = new Date();
                        handleDateSelection(today);
                      }}
                    >
                      <span className="chip-label">Today</span>
                      <span className="chip-date">{new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
                    </button>
                    <button
                      className="date-chip"
                      onClick={() => {
                        const tomorrow = new Date();
                        tomorrow.setDate(tomorrow.getDate() + 1);
                        handleDateSelection(tomorrow);
                      }}
                    >
                      <span className="chip-label">Tomorrow</span>
                      <span className="chip-date">{(() => {
                        const tomorrow = new Date();
                        tomorrow.setDate(tomorrow.getDate() + 1);
                        return tomorrow.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                      })()}</span>
                    </button>
                    <button
                      className="date-chip"
                      onClick={() => {
                        const nextWeek = new Date();
                        nextWeek.setDate(nextWeek.getDate() + 7);
                        handleDateSelection(nextWeek);
                      }}
                    >
                      <span className="chip-label">Next Week</span>
                      <span className="chip-date">{(() => {
                        const nextWeek = new Date();
                        nextWeek.setDate(nextWeek.getDate() + 7);
                        return nextWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                      })()}</span>
                    </button>
                  </div>

                  {/* Compact Date Input */}
                  <div className="date-input-container">
                    <span className="date-input-label">Or pick a specific date:</span>
                    <div className="date-input-wrapper">
                      <DatePicker
                        selected={selectedDate}
                        onChange={(date) => setSelectedDate(date)}
                        dateFormat="MMM d, yyyy"
                        className="compact-datepicker-input"
                        placeholderText="Select date"
                        minDate={new Date()}
                        showPopperArrow={true}
                        popperPlacement="top"
                      />
                      {selectedDate && (
                        <button
                          className="date-confirm-btn"
                          onClick={() => handleDateSelection(selectedDate)}
                        >
                          ✓
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Cancel Option */}
                  <div className="date-picker-footer">
                    <button
                      className="date-cancel-link"
                      onClick={() => {
                        setShowDatePicker(false);
                        setSelectedDate(null);
                        setChatHistory((prev) => [
                          ...prev,
                          { sender: "bot", text: "No problem! Feel free to ask me anything else. 😊" }
                        ]);
                      }}
                    >
                      Maybe later
                    </button>
                  </div>
                </div>
              </div>
            )}
  
            {showUpload && uploadStatus === 'idle' && (
              <div className="chat-message bot enhanced-upload-container">
                <p className="upload-heading">📎 Upload your ticket file</p>
                <div className="upload-box-ui">
                  <label htmlFor="file-upload" className="upload-button">
                    <input 
                      type="file" 
                      id="file-upload" 
                      onChange={handleFileUpload} 
                      className="upload-input" 
                    />
                    <span>📂 Browse File</span>
                  </label>
                  {uploadedFile && (
                    <div className="file-preview">
                      <span className="file-icon">📄</span> {uploadedFile.name}
                    </div>
                  )}
                  <Button 
                    appearance="primary" 
                    color="green" 
                    className="confirm-upload-btn" 
                    onClick={submitUpload} 
                    disabled={!uploadedFile}
                  >
                    Upload & Continue
                  </Button>
                </div>
              </div>
            )}
            
            {(uploadStatus === 'reading' || uploadStatus === 'processing') && (
              <ProcessingIndicator />
            )}

            {/* PNR Processing Animation */}
            <PnrProcessingAnimation />
  
            {isTyping && (
              <div className="chat-message bot typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            )}

          </div>

          {/* Scroll to Bottom Button - Positioned relative to chat-window */}
          <div
            className="scroll-to-bottom-container"
            style={{
              position: 'absolute',
              bottom: '80px', // Above the input section
              right: '20px',
              zIndex: 1000,
              display: 'block',
              opacity: showScrollToBottom ? '1' : '0.3' // Always visible but lighter when not needed
            }}
            title={`Scroll to bottom (Active: ${showScrollToBottom})`}
          >
            <button
              className="scroll-to-bottom-btn"
              onClick={handleScrollToBottomClick}
              title="Scroll to bottom"
              style={{
                background: showScrollToBottom ? '#d71921' : 'rgba(128, 128, 128, 0.3)',
                border: showScrollToBottom ? '2px solid #d71921' : '2px solid rgba(128, 128, 128, 0.5)',
                borderRadius: '50%',
                width: '44px',
                height: '44px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                color: showScrollToBottom ? 'white' : 'rgba(100, 100, 100, 0.8)',
                boxShadow: showScrollToBottom ? '0 6px 16px rgba(215, 25, 33, 0.4)' : '0 2px 8px rgba(0, 0, 0, 0.1)',
                fontSize: '20px',
                fontWeight: 'bold',
                transition: 'all 0.2s ease',
                transform: showScrollToBottom ? 'scale(1.1)' : 'scale(1)'
              }}
            >
              ↓
            </button>
          </div>

          <div className="chat-input-section" style={{
            padding: '16px',
            background: 'white',
            borderTop: '1px solid rgba(215, 25, 33, 0.1)',
            display: 'flex',
            gap: '12px',
            alignItems: 'center',
            position: 'relative',
            zIndex: 1,
            flexShrink: 0
          }}>
            <input
              type="text"
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              placeholder="Type a message..."
              className="chat-input"
              onKeyDown={(e) => e.key === "Enter" && sendMessage()}
              style={{
                flex: 1,
                padding: '12px 16px',
                border: '1px solid rgba(215, 25, 33, 0.2)',
                borderRadius: '24px',
                outline: 'none',
                fontSize: '14px'
              }}
            />
            <button
              onClick={sendMessage}
              className="chat-send-button"
              style={{
                background: '#d71921',
                color: 'white',
                border: 'none',
                padding: '10px 20px',
                borderRadius: '20px',
                cursor: 'pointer',
                fontWeight: '500'
              }}
            >
              Send
            </button>
          </div>
            </div>
        </Resizable>
       
      </div>
    ) : (
      <Button circle className="chat-toggle-button" onClick={toggleChat} appearance="primary">
        <FiMessageSquare size={24} />
      </Button>
    )}
  </div>
  );
};

// Utility function for formatting time (moved outside component)
const formatTime = (timeStr) =>
  new Date(timeStr).toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  });

// Flight Booking Interface Component
const FlightBookingInterface = ({ flight, onConfirm, onCancel }) => {
  const [passengerDetails, setPassengerDetails] = useState({
    name: '', email: '', phone: '', age: '', gender: ''
  });
  const [isFormValid, setIsFormValid] = useState(false);

  // Real-time validation
  React.useEffect(() => {
    const { name, email, phone } = passengerDetails;
    setIsFormValid(name.trim() && email.trim() && phone.trim());
  }, [passengerDetails]);

  const handleInputChange = (field, value) => {
    setPassengerDetails(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleConfirmBooking = () => {
    if (isFormValid) {
      onConfirm(flight, passengerDetails);
    }
  };

  return (
    <div className="flight-booking-interface">
      <div className="booking-header">
        <h4>✈️ Complete Your Booking</h4>
        <p>Please provide passenger details for your flight</p>
      </div>

      {/* Flight Summary */}
      <div className="booking-flight-summary">
        <div className="flight-info">
          <div className="airline-info">
            <span className="airline-name">{flight.airline}</span>
            <span className="flight-number">Flight {flight.id}</span>
          </div>
          <div className="flight-times">
            <span>🛫 {formatTime(flight.departureTime)}</span>
            <span>🛬 {formatTime(flight.arrivalTime)}</span>
          </div>
          <div className="flight-price">
            <span className="price-label">Total Price:</span>
            <span className="price-amount">₹{flight.price.toLocaleString()}</span>
          </div>
        </div>
      </div>

      {/* Passenger Details Form */}
      <div className="passenger-form">
        <h5>👤 Passenger Information</h5>

        <div className="form-row">
          <div className="form-group">
            <label>Full Name *</label>
            <input
              type="text"
              placeholder="Enter full name"
              value={passengerDetails.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="form-input"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label>Email Address *</label>
            <input
              type="email"
              placeholder="Enter email"
              value={passengerDetails.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="form-input"
            />
          </div>
          <div className="form-group">
            <label>Phone Number *</label>
            <input
              type="tel"
              placeholder="Enter phone"
              value={passengerDetails.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="form-input"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label>Age</label>
            <input
              type="number"
              placeholder="Age"
              value={passengerDetails.age}
              onChange={(e) => handleInputChange('age', e.target.value)}
              className="form-input"
              min="1"
              max="120"
            />
          </div>
          <div className="form-group">
            <label>Gender</label>
            <select
              value={passengerDetails.gender}
              onChange={(e) => handleInputChange('gender', e.target.value)}
              className="form-input"
            >
              <option value="">Select</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
            </select>
          </div>
        </div>
      </div>

      {/* Booking Actions */}
      <div className="booking-actions">
        <button
          className="cancel-booking-btn"
          onClick={onCancel}
        >
          Cancel
        </button>
        <button
          className={`confirm-booking-btn ${!isFormValid ? 'disabled' : ''}`}
          onClick={handleConfirmBooking}
          disabled={!isFormValid}
        >
          {isFormValid ? `Book for ₹${flight.price.toLocaleString()}` : 'Fill Required Fields'}
        </button>
      </div>
    </div>
  );
};

// Booking Confirmation Interface Component
const BookingConfirmationInterface = ({ flight, passenger, bookingReference }) => {
  return (
    <div className="booking-confirmation-interface">
      <div className="confirmation-header">
        <div className="success-icon">🎉</div>
        <h4>Booking Confirmed!</h4>
        <p>Your flight has been successfully booked</p>
      </div>

      <div className="confirmation-details">
        <div className="booking-ref-section">
          <div className="booking-ref">
            <span className="ref-label">Booking Reference</span>
            <span className="ref-number">{bookingReference}</span>
          </div>
        </div>

        <div className="passenger-section">
          <h5>👤 Passenger Details</h5>
          <div className="detail-row">
            <span className="label">Name:</span>
            <span className="value">{passenger.name}</span>
          </div>
          <div className="detail-row">
            <span className="label">Email:</span>
            <span className="value">{passenger.email}</span>
          </div>
          <div className="detail-row">
            <span className="label">Phone:</span>
            <span className="value">{passenger.phone}</span>
          </div>
        </div>

        <div className="flight-section">
          <h5>✈️ Flight Details</h5>
          <div className="detail-row">
            <span className="label">Airline:</span>
            <span className="value">{flight.airline}</span>
          </div>
          <div className="detail-row">
            <span className="label">Flight:</span>
            <span className="value">{flight.id}</span>
          </div>
          <div className="detail-row">
            <span className="label">Departure:</span>
            <span className="value">{formatTime(flight.departureTime)}</span>
          </div>
          <div className="detail-row">
            <span className="label">Arrival:</span>
            <span className="value">{formatTime(flight.arrivalTime)}</span>
          </div>
          <div className="detail-row">
            <span className="label">Total Paid:</span>
            <span className="value price">₹{flight.price.toLocaleString()}</span>
          </div>
        </div>
      </div>

      <div className="confirmation-actions">
        <div className="next-steps">
          <p>📧 Confirmation email sent to {passenger.email}</p>
          <p>📱 SMS confirmation sent to {passenger.phone}</p>
          <p>🎫 E-ticket will be available in your email shortly</p>
        </div>

        <div className="action-buttons">
          <button className="download-ticket-btn">
            📄 Download E-Ticket
          </button>
          <button className="manage-booking-btn">
            ✏️ Manage Booking
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatWidget;
